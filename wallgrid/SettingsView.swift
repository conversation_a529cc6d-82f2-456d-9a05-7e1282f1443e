import SwiftUI

struct SettingsView: View {
    @StateObject private var settingsManager = SettingsManager.shared
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        VStack(spacing: 25) {
            // عنوان النافذة
            HStack {
                Text("Settings")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Spacer()

                Button(action: {
                    closeSettings()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.secondary)
                }
                .buttonStyle(PlainButtonStyle())
                .onHover { hovering in
                    NSCursor.pointingHand.set()
                }
            }
            .padding(.horizontal, 30)
            .padding(.top, 25)
            
            Divider()
                .padding(.horizontal, 30)

            // محتوى الإعدادات
            VStack(alignment: .leading, spacing: 20) {
                // قسم التشغيل التلقائي
                VStack(alignment: .leading, spacing: 15) {
                    Text("Startup")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    HStack {
                        Toggle("Launch at login", isOn: $settingsManager.launchAtLogin)
                            .toggleStyle(SwitchToggleStyle(tint: .blue))
                            .font(.body)

                        Spacer()
                    }

                    Text("When enabled, WallGrid will automatically start in the menu bar when you log in to your computer.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.leading, 25)
                        .fixedSize(horizontal: false, vertical: true)
                }
                .padding(.horizontal, 30)
                
                Spacer()

                // أزرار الإجراءات
                HStack {
                    Spacer()

                    Button("Reset") {
                        settingsManager.resetToDefaults()
                    }
                    .buttonStyle(PlainButtonStyle())
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(Color.red.opacity(0.1))
                    .foregroundColor(.red)
                    .cornerRadius(8)
                    .onHover { hovering in
                        NSCursor.pointingHand.set()
                    }

                    Button("Done") {
                        closeSettings()
                    }
                    .buttonStyle(PlainButtonStyle())
                    .padding(.horizontal, 25)
                    .padding(.vertical, 10)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                    .onHover { hovering in
                        NSCursor.pointingHand.set()
                    }
                }
                .padding(.horizontal, 30)
                .padding(.bottom, 25)
            }
        }
        .frame(width: 500, height: 320)
        .background(Color(NSColor.windowBackgroundColor))
        .cornerRadius(15)
        .onAppear {
            // التحقق من حالة التشغيل التلقائي عند فتح النافذة
            settingsManager.launchAtLogin = settingsManager.checkLaunchAtLoginStatus()
        }
    }
    
    private func closeSettings() {
        // إغلاق نافذة الإعدادات
        if let window = NSApplication.shared.windows.first(where: { $0.title == "Settings" }) {
            window.close()
        }
    }
}

// نافذة الإعدادات المخصصة
class SettingsWindowController: NSWindowController {
    convenience init() {
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 500, height: 320),
            styleMask: [.titled, .closable],
            backing: .buffered,
            defer: false
        )

        window.title = "Settings"
        window.center()
        window.isReleasedWhenClosed = false
        window.level = .floating
        
        // تعيين محتوى النافذة
        let hostingController = NSHostingController(rootView: SettingsView())
        window.contentViewController = hostingController
        
        self.init(window: window)
    }
    
    func showSettings() {
        window?.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
    }
}

#Preview {
    SettingsView()
}
