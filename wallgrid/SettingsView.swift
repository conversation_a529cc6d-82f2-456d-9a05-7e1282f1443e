import SwiftUI

struct SettingsView: View {
    @StateObject private var settingsManager = SettingsManager.shared
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        VStack(spacing: 20) {
            // عنوان النافذة
            HStack {
                Text("الإعدادات")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button(action: {
                    closeSettings()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.secondary)
                }
                .buttonStyle(PlainButtonStyle())
                .onHover { hovering in
                    NSCursor.pointingHand.set()
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            
            Divider()
                .padding(.horizontal, 20)
            
            // محتوى الإعدادات
            VStack(alignment: .leading, spacing: 15) {
                // قسم التشغيل التلقائي
                VStack(alignment: .leading, spacing: 10) {
                    Text("التشغيل التلقائي")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    HStack {
                        Toggle("تشغيل التطبيق تلقائياً عند بدء تشغيل الكمبيوتر", isOn: $settingsManager.launchAtLogin)
                            .toggleStyle(SwitchToggleStyle(tint: .blue))
                        
                        Spacer()
                    }
                    
                    Text("عند تفعيل هذا الخيار، سيتم تشغيل WallGrid تلقائياً في شريط القوائم عند بدء تشغيل النظام.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.leading, 20)
                }
                .padding(.horizontal, 20)
                
                Spacer()
                
                // أزرار الإجراءات
                HStack {
                    Spacer()
                    
                    Button("إعادة تعيين") {
                        settingsManager.resetToDefaults()
                    }
                    .buttonStyle(PlainButtonStyle())
                    .padding(.horizontal, 15)
                    .padding(.vertical, 8)
                    .background(Color.red.opacity(0.1))
                    .foregroundColor(.red)
                    .cornerRadius(8)
                    .onHover { hovering in
                        NSCursor.pointingHand.set()
                    }
                    
                    Button("تم") {
                        closeSettings()
                    }
                    .buttonStyle(PlainButtonStyle())
                    .padding(.horizontal, 20)
                    .padding(.vertical, 8)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                    .onHover { hovering in
                        NSCursor.pointingHand.set()
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 20)
            }
        }
        .frame(width: 400, height: 250)
        .background(
            VisualEffectView(material: .hudWindow, blendingMode: .behindWindow)
        )
        .cornerRadius(15)
        .onAppear {
            // التحقق من حالة التشغيل التلقائي عند فتح النافذة
            settingsManager.launchAtLogin = settingsManager.checkLaunchAtLoginStatus()
        }
    }
    
    private func closeSettings() {
        // إغلاق نافذة الإعدادات
        if let window = NSApplication.shared.windows.first(where: { $0.title == "الإعدادات" }) {
            window.close()
        }
    }
}

// نافذة الإعدادات المخصصة
class SettingsWindowController: NSWindowController {
    convenience init() {
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 400, height: 250),
            styleMask: [.titled, .closable],
            backing: .buffered,
            defer: false
        )
        
        window.title = "الإعدادات"
        window.center()
        window.isReleasedWhenClosed = false
        window.level = .floating
        
        // تعيين محتوى النافذة
        let hostingController = NSHostingController(rootView: SettingsView())
        window.contentViewController = hostingController
        
        self.init(window: window)
    }
    
    func showSettings() {
        window?.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
    }
}

#Preview {
    SettingsView()
}
