import SwiftUI
import AppKit

struct WallGridView: View {
    @State private var wallpaperBoxes: [WallpaperBox] = Array(0..<10).map { WallpaperBox(id: $0) }

    var body: some View {
        ScrollView(showsIndicators: false) {
            LazyVGrid(columns: [
                GridItem(.fixed(180), spacing: 10),
                GridItem(.fixed(180), spacing: 10)
            ], spacing: 10) {
                ForEach(wallpaperBoxes.indices, id: \.self) { index in
                    GridBoxView(
                        wallpaperBox: $wallpaperBoxes[index],
                        onChooseImages: { chooseImages() }
                    )
                }
            }
        }
        .cornerRadius(35)
        .padding(10)
        .frame(width: 390, height: 270)
        .background(
            VisualEffectView(material: .hudWindow, blendingMode: .behindWindow)
        )
        .cornerRadius(40)
    }

    private func chooseImages() {
        let openPanel = NSOpenPanel()
        openPanel.title = "اختر صور خلفية"
        openPanel.canChooseFiles = true
        openPanel.canChooseDirectories = false
        openPanel.allowsMultipleSelection = true
        openPanel.allowedContentTypes = [.image]

        // تعيين المجلد الافتراضي إلى مجلد الصور
        if let picturesURL = FileManager.default.urls(for: .picturesDirectory, in: .userDomainMask).first {
            openPanel.directoryURL = picturesURL
        }

        openPanel.begin { response in
            if response == .OK {
                let urls = openPanel.urls

                DispatchQueue.main.async {
                    // إضافة الصور إلى المربعات الفارغة
                    self.addImagesToEmptyBoxes(urls: urls)
                }
            }
        }
    }

    private func addImagesToEmptyBoxes(urls: [URL]) {
        var urlIndex = 0

        for i in 0..<wallpaperBoxes.count {
            // إذا كان المربع فارغ ولدينا صور متبقية
            if wallpaperBoxes[i].selectedImage == nil && urlIndex < urls.count {
                let url = urls[urlIndex]

                if let image = NSImage(contentsOf: url) {
                    // تحسين جودة الصورة للعرض
                    let optimizedImage = createHighQualityThumbnail(from: image)
                    wallpaperBoxes[i].selectedImage = optimizedImage
                    wallpaperBoxes[i].imagePath = url.path

                    urlIndex += 1
                }
            }
        }
    }

    private func createHighQualityThumbnail(from image: NSImage) -> NSImage {
        // حساب الحجم المناسب مع الحفاظ على النسبة
        let targetSize = NSSize(width: 360, height: 240) // ضعف الحجم للحصول على دقة أعلى
        let imageSize = image.size

        let widthRatio = targetSize.width / imageSize.width
        let heightRatio = targetSize.height / imageSize.height
        let ratio = max(widthRatio, heightRatio) // استخدام max للتأكد من ملء المساحة

        let newSize = NSSize(
            width: imageSize.width * ratio,
            height: imageSize.height * ratio
        )

        let thumbnail = NSImage(size: newSize)
        thumbnail.lockFocus()

        // إعدادات عالية الجودة
        let context = NSGraphicsContext.current
        context?.imageInterpolation = .high
        context?.shouldAntialias = true
        context?.compositingOperation = .copy

        // تحسين جودة الرسم باستخدام Core Graphics
        if let cgContext = context?.cgContext {
            cgContext.interpolationQuality = .high
            cgContext.setShouldAntialias(true)
            cgContext.setShouldSmoothFonts(true)
            cgContext.setAllowsAntialiasing(true)
            cgContext.setShouldSubpixelPositionFonts(true)
            cgContext.setShouldSubpixelQuantizeFonts(true)
        }

        // رسم الصورة بجودة عالية
        image.draw(
            in: NSRect(origin: .zero, size: newSize),
            from: NSRect(origin: .zero, size: image.size),
            operation: .copy,
            fraction: 1.0
        )

        thumbnail.unlockFocus()
        return thumbnail
    }
}

struct WallpaperBox {
    let id: Int
    var selectedImage: NSImage?
    var imagePath: String?

    init(id: Int) {
        self.id = id
        self.selectedImage = nil
        self.imagePath = nil
    }
}

struct GridBoxView: View {
    @Binding var wallpaperBox: WallpaperBox
    let onChooseImages: () -> Void

    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 35)
                .fill(Color.white.opacity(0.10))
                .frame(width: 180, height: 120)

            if let selectedImage = wallpaperBox.selectedImage {
                // عرض الصورة المختارة بجودة عالية
                Image(nsImage: selectedImage)
                    .resizable()
                    .interpolation(.high)
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 180, height: 120)
                    .clipped()
                    .cornerRadius(35)
                    .drawingGroup()
                    .onTapGesture {
                        applyWallpaper()
                    }
            } else {
                // مربع فارغ
                VStack {
                    Image(systemName: "plus.circle")
                        .font(.title)
                        .foregroundColor(.blue)
                    Text("مربع \(wallpaperBox.id + 1)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .onTapGesture {
                    chooseImages()
                }
            }
        }
        .contextMenu {
            if wallpaperBox.selectedImage != nil {
                Button("تطبيق كخلفية") {
                    applyWallpaper()
                }
                Button("حذف الصورة") {
                    wallpaperBox.selectedImage = nil
                    wallpaperBox.imagePath = nil
                }
                Button("اختيار صورة جديدة") {
                    chooseImages()
                }
            } else {
                Button("اختيار صور") {
                    chooseImages()
                }
            }
        }
    }



    private func createHighQualityThumbnail(from image: NSImage) -> NSImage {
        // حساب الحجم المناسب مع الحفاظ على النسبة
        let targetSize = NSSize(width: 360, height: 240) // ضعف الحجم للحصول على دقة أعلى
        let imageSize = image.size

        let widthRatio = targetSize.width / imageSize.width
        let heightRatio = targetSize.height / imageSize.height
        let ratio = max(widthRatio, heightRatio) // استخدام max للتأكد من ملء المساحة

        let newSize = NSSize(
            width: imageSize.width * ratio,
            height: imageSize.height * ratio
        )

        let thumbnail = NSImage(size: newSize)
        thumbnail.lockFocus()

        // إعدادات عالية الجودة
        let context = NSGraphicsContext.current
        context?.imageInterpolation = .high
        context?.shouldAntialias = true
        context?.compositingOperation = .copy

        // تحسين جودة الرسم باستخدام Core Graphics
        if let cgContext = context?.cgContext {
            cgContext.interpolationQuality = .high
            cgContext.setShouldAntialias(true)
            cgContext.setShouldSmoothFonts(true)
            cgContext.setAllowsAntialiasing(true)
            cgContext.setShouldSubpixelPositionFonts(true)
            cgContext.setShouldSubpixelQuantizeFonts(true)
        }

        // رسم الصورة بجودة عالية
        image.draw(
            in: NSRect(origin: .zero, size: newSize),
            from: NSRect(origin: .zero, size: image.size),
            operation: .copy,
            fraction: 1.0
        )

        thumbnail.unlockFocus()
        return thumbnail
    }

    private func applyWallpaper() {
        guard let imagePath = wallpaperBox.imagePath else { return }

        let url = URL(fileURLWithPath: imagePath)

        do {
            try NSWorkspace.shared.setDesktopImageURL(url, for: NSScreen.main!, options: [:])
            print("تم تطبيق الخلفية بنجاح!")
        } catch {
            print("خطأ في تطبيق الخلفية: \(error)")
        }
    }
}

// امتداد لتحسين جودة الصور
extension NSImage {
    /// إنشاء نسخة عالية الجودة من الصورة
    func highQualityRepresentation() -> NSImage {
        guard let tiffData = self.tiffRepresentation,
              let bitmapRep = NSBitmapImageRep(data: tiffData) else {
            return self
        }

        // إنشاء صورة جديدة بدقة أعلى
        let newImage = NSImage(size: self.size)
        newImage.addRepresentation(bitmapRep)

        return newImage
    }

    /// تحسين الصورة للعرض في الواجهة
    func optimizedForDisplay(targetSize: NSSize) -> NSImage {
        let optimized = NSImage(size: targetSize)
        optimized.lockFocus()

        // إعدادات متقدمة للجودة
        let context = NSGraphicsContext.current
        context?.imageInterpolation = .high
        context?.shouldAntialias = true
        context?.compositingOperation = .sourceOver

        // رسم بجودة عالية
        self.draw(
            in: NSRect(origin: .zero, size: targetSize),
            from: NSRect(origin: .zero, size: self.size),
            operation: .copy,
            fraction: 1.0
        )

        optimized.unlockFocus()
        return optimized
    }
}

// Visual Effect View لتطبيق تأثير blur
struct VisualEffectView: NSViewRepresentable {
    let material: NSVisualEffectView.Material
    let blendingMode: NSVisualEffectView.BlendingMode

    func makeNSView(context: Context) -> NSVisualEffectView {
        let visualEffectView = NSVisualEffectView()
        visualEffectView.material = material
        visualEffectView.blendingMode = blendingMode
        visualEffectView.state = .active
        return visualEffectView
    }

    func updateNSView(_ visualEffectView: NSVisualEffectView, context: Context) {
        visualEffectView.material = material
        visualEffectView.blendingMode = blendingMode
    }
}

#Preview {
    WallGridView()
}
